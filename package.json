{"name": "nursadi-developing", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "date-fns": "^4.1.0", "http-proxy-middleware": "^3.0.5", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "styled-components": "^6.1.17", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "source-map-explorer 'build/static/js/*.js'", "predeploy": "npm run build", "deploy": "gh-pages -d build", "deploy:netlify": "netlify deploy --prod", "deploy:vercel": "vercel --prod"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"gh-pages": "^6.1.1", "source-map-explorer": "^2.5.3"}}