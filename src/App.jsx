import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import Layout from "./components/Layout";
import PriceList from "./components/PriceList";
import ProductDetail from "./components/ProductDetail";
import Cart from "./components/Cart";
import Auth from "./components/Auth";
import CreateTender from "./components/CreateTender";
import TenderForm from "./components/TenderForm";
import { CartProvider } from "./context/CartContext";

function App() {
  return (
    <CartProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<PriceList />} />
            <Route path="price-list" element={<PriceList />} />
            <Route path="product/:productId" element={<ProductDetail />} />
            <Route path="cart" element={<Cart />} />
            <Route path="auth" element={<Auth />} />
            <Route path="create-tender" element={<CreateTender />} />
            <Route path="tender-form" element={<TenderForm />} />
          </Route>
        </Routes>
        {/* Добавляем DevTools для React Query (только в режиме разработки) */}
        <ReactQueryDevtools initialIsOpen={false} position="bottom-right" />
      </BrowserRouter>
    </CartProvider>
  );
}

export default App;
