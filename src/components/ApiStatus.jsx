import React, { useState, useEffect } from "react";
import styled from "styled-components";
import ApiService from "../services/api.service.js";

const StatusContainer = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  max-width: 300px;
  z-index: 1000;
`;

const StatusTitle = styled.h4`
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
`;

const StatusItem = styled.div`
  margin: 4px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StatusLabel = styled.span`
  color: #666;
  margin-right: 8px;
`;

const StatusValue = styled.span`
  color: #333;
  font-weight: 500;
`;

const StatusBadge = styled.span`
  background: ${props => 
    props.type === 'production' ? '#dc3545' : 
    props.type === 'test' ? '#28a745' : 
    props.authenticated ? '#28a745' : '#ffc107'
  };
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  text-transform: uppercase;
`;

const ToggleButton = styled.button`
  background: none;
  border: none;
  color: #0066cc;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
  padding: 0;
  margin-top: 8px;
  
  &:hover {
    color: #0056b3;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  
  &:hover {
    color: #666;
  }
`;

const ApiStatus = ({ onClose }) => {
  const [apiInfo, setApiInfo] = useState(null);
  const [cacheStats, setCacheStats] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    updateInfo();
  }, []);

  const updateInfo = () => {
    const info = ApiService.getApiInfo();
    const stats = ApiService.getCacheStats();
    setApiInfo(info);
    setCacheStats(stats);
  };

  const handleClearCache = () => {
    ApiService.clearCache();
    updateInfo();
  };

  if (!apiInfo) {
    return null;
  }

  return (
    <StatusContainer>
      <CloseButton onClick={onClose}>×</CloseButton>
      
      <StatusTitle>API Status</StatusTitle>
      
      <StatusItem>
        <StatusLabel>Тип API:</StatusLabel>
        <StatusBadge type={apiInfo.apiType}>
          {apiInfo.apiType}
        </StatusBadge>
      </StatusItem>
      
      <StatusItem>
        <StatusLabel>URL:</StatusLabel>
        <StatusValue>{apiInfo.baseUrl}</StatusValue>
      </StatusItem>
      
      {apiInfo.authRequired && (
        <StatusItem>
          <StatusLabel>Авторизация:</StatusLabel>
          <StatusBadge authenticated={apiInfo.authStatus.authenticated}>
            {apiInfo.authStatus.message}
          </StatusBadge>
        </StatusItem>
      )}
      
      <ToggleButton onClick={() => setShowDetails(!showDetails)}>
        {showDetails ? 'Скрыть детали' : 'Показать детали'}
      </ToggleButton>
      
      {showDetails && (
        <>
          <StatusTitle style={{ marginTop: '12px' }}>Кэш</StatusTitle>
          {Object.entries(cacheStats).map(([key, value]) => (
            <StatusItem key={key}>
              <StatusLabel>{key}:</StatusLabel>
              <StatusValue>{value}</StatusValue>
            </StatusItem>
          ))}
          
          <ToggleButton onClick={handleClearCache}>
            Очистить кэш
          </ToggleButton>
          
          <StatusTitle style={{ marginTop: '12px' }}>Endpoints</StatusTitle>
          {Object.entries(apiInfo.endpoints).map(([key, value]) => (
            <StatusItem key={key}>
              <StatusLabel>{key}:</StatusLabel>
              <StatusValue>{value}</StatusValue>
            </StatusItem>
          ))}
        </>
      )}
    </StatusContainer>
  );
};

export default ApiStatus;
