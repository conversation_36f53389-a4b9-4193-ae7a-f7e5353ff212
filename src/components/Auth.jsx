import React, { useState } from "react";
import styled from "styled-components";

const AuthContainer = styled.div`
  padding: 24px;
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 120px);
  background-color: #f8f9fa;

  @media (max-width: 768px) {
    padding: 16px;
    min-height: calc(100vh - 80px);
  }
`;

const AuthCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  @media (max-width: 768px) {
    padding: 24px;
    border-radius: 8px;
  }
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 32px;
  border-bottom: 1px solid #e9ecef;
`;

const Tab = styled.button`
  flex: 1;
  padding: 12px 0;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.active ? '#007bff' : '#6c757d'};
  border-bottom: 2px solid ${props => props.active ? '#007bff' : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #007bff;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InputField = styled.input`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #007bff;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
`;

const SubmitButton = styled.button`
  width: 100%;
  padding: 14px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 8px;

  &:hover {
    background-color: #5a6268;
  }

  &:active {
    background-color: #545b62;
  }
`;

const SocialButton = styled.button`
  width: 100%;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
  }

  &:active {
    background-color: #e9ecef;
  }
`;

const SocialButtonsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 24px;
`;

const GoogleIcon = styled.div`
  width: 18px;
  height: 18px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
`;

const VKIcon = styled.div`
  width: 18px;
  height: 18px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234C75A3' d='M15.684 0H8.316C1.592 0 0 1.592 0 8.316v7.368C0 22.408 1.592 24 8.316 24h7.368C22.408 24 24 22.408 24 15.684V8.316C24 1.592 22.408 0 15.684 0zm3.692 17.123h-1.744c-.66 0-.864-.525-2.05-1.727-1.033-1.01-1.49-.9-1.49.402v1.15c0 .3-.096.483-.943.483-1.946 0-4.1-1.173-5.617-3.358-2.289-3.063-2.913-5.371-2.913-5.846 0-.33.134-.477.44-.477h1.743c.33 0 .453.134.58.447.637 1.56 1.706 2.925 2.14 1.846.346-.857.2-2.756-.174-3.084-.3-.267-.863-.133-1.117-.133 0 0 .3-.9 1.394-.9 1.394 0 2.41.67 2.41 1.73v2.264c0 .564.257.68.42.68.33 0 .896-.2 2.23-1.73 1.01-1.16 1.76-2.957 1.76-2.957.134-.267.334-.4.668-.4h1.744c.66 0 .8.337.668.8 0 0-.8 1.96-2.264 3.424-.896 1.01-.67 1.51.2 2.41 1.01 1.01 1.73 1.96 1.73 2.59.033.33-.167.497-.497.497z'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
`;

const Auth = () => {
  const [activeTab, setActiveTab] = useState("login");
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Здесь будет логика авторизации
  };

  const handleGoogleAuth = () => {
    console.log("Google auth clicked");
    // Здесь будет логика авторизации через Google
  };

  const handleVKAuth = () => {
    console.log("VK auth clicked");
    // Здесь будет логика авторизации через VK
  };

  return (
    <AuthContainer>
      <AuthCard>
        <TabContainer>
          <Tab 
            active={activeTab === "login"} 
            onClick={() => setActiveTab("login")}
          >
            Вход
          </Tab>
          <Tab 
            active={activeTab === "register"} 
            onClick={() => setActiveTab("register")}
          >
            Регистрация
          </Tab>
        </TabContainer>

        <Form onSubmit={handleSubmit}>
          <InputField
            type="email"
            name="email"
            placeholder="Введите адрес электронной почты"
            value={formData.email}
            onChange={handleInputChange}
            required
          />
          
          <InputField
            type="password"
            name="password"
            placeholder="Придумайте пароль"
            value={formData.password}
            onChange={handleInputChange}
            required
          />

          <SubmitButton type="submit">
            Войти
          </SubmitButton>
        </Form>

        <SocialButtonsContainer>
          <SocialButton onClick={handleGoogleAuth}>
            <GoogleIcon />
            Войти через Google
          </SocialButton>
          
          <SocialButton onClick={handleVKAuth}>
            <VKIcon />
            Войти через VK
          </SocialButton>
        </SocialButtonsContainer>
      </AuthCard>
    </AuthContainer>
  );
};

export default Auth;
