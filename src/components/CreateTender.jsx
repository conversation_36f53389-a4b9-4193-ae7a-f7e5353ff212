import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { useSearchProducts } from "../hooks/useApi";
import ProductCard from "./ProductCard";
import ProductCardSkeleton from "./ProductCardSkeleton";

const CreateTenderContainer = styled.div`
  /* padding: 0 10px; */
`;
CreateTenderContainer.displayName = "CreateTenderContainer";

const SelectedMainPanel = styled.div`
  border-bottom: 1px solid #dfe4e5;
`;
SelectedMainPanel.displayName = "SelectedMainPanel";

const SelectedProductsPanel = styled.div`
  display: flex;
  margin: 0 auto;
  max-width: 1150px;
  padding: 12px 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
`;
SelectedProductsPanel.displayName = "SelectedProductsPanel";

const SelectedProductsInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
`;
SelectedProductsInfo.displayName = "SelectedProductsInfo";

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;
DropdownContainer.displayName = "DropdownContainer";

const PositionsButton = styled.button`
  background-color: white;
  color: #0066cc;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(0, 91, 209, 0.1);
  }
`;
PositionsButton.displayName = "PositionsButton";

const DropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
  min-width: 350px;
`;
DropdownList.displayName = "DropdownList";

const DropdownItem = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #333;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8f9fa;
  }

  .product-name {
    flex: 1;
    margin-right: 12px;
  }

  .remove-btn {
    background: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 26px;
    transition: all 0.3s ease;

    &:hover {
      color: #dc3545;
      background-color: #f8f9fa;
    }
  }
`;
DropdownItem.displayName = "DropdownItem";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButton = styled.button`
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const SelectedProductsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
  width: 100%;
`;
SelectedProductsList.displayName = "SelectedProductsList";

const SelectedProductTag = styled.div`
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 300px;

  .product-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .remove-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    font-size: 16px;
    line-height: 1;

    &:hover {
      color: #dc3545;
    }
  }
`;
SelectedProductTag.displayName = "SelectedProductTag";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  text-align: center;
  margin-top: 32px;
  color: #434a54;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const IconsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 45px;
  margin-top: 32px;

  @media (max-width: 768px) {
    justify-content: flex-start; /* выравнивание по левому краю */
    gap: 16px;
    margin-left: 16px;
  }

  @media (max-width: 480px) {
    justify-content: flex-start; /* сохраняем выравнивание слева */
    gap: 10px;
    margin-left: 16px;
  }
`;
IconsContainer.displayName = "IconsContainer";

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: white;
  border-radius: 8px;
`;
IconWrapper.displayName = "IconWrapper";

const IconBlock = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
  text-align: center;
`;

IconBlock.displayName = "IconBlock";

const IconLabel = styled.div`
  margin-top: 8px;
  font-size: 14px;
  color: #343a40;
`;

IconLabel.displayName = "IconLabel";

const Icon = styled.img`
  svg {
    width: 24px;
    height: 24px;
  }
`;
Icon.displayName = "Icon";

const SearchSection = styled.div`
  border-bottom: 1px solid #dee2e6;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;
SearchSection.displayName = "SearchSection";

const SearchContainer = styled.div`
  max-width: 820px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  margin-top: 32px;
`;
SearchContainer.displayName = "SearchContainer";

const ResultsCounter = styled.div`
  color: #6c757d;
  font-size: 14px;
`;
ResultsCounter.displayName = "ResultsCounter";

const SearchIcon = styled.div`
  position: absolute;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
`;
SearchIcon.displayName = "SearchIcon";

const SearchInput = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 16px;
  background-color: white;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;
SearchInput.displayName = "SearchInput";

const ClearButton = styled.button`
  position: absolute;
  right: 58px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #adb5bd;
  cursor: pointer;
  font-size: 25px;

  &:hover {
    color: #6c757d;
  }
`;
ClearButton.displayName = "ClearButton";

const TabsContainer = styled.div`
  display: flex;
  max-width: 800px;
  margin: 0 auto;
  overflow-x: auto;
  margin-top: 20px;

  @media (max-width: 768px) {
    gap: 0;
  }
`;
TabsContainer.displayName = "TabsContainer";

const Tab = styled.button`
  padding: 16px 24px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;

  ${(props) =>
    props.active &&
    `
    color: #0066cc;
    border-bottom-color: #0066cc;
  `}

  &:hover {
    color: #0066cc;
  }

  @media (max-width: 768px) {
    padding: 12px 16px;
    font-size: 13px;
  }
`;
Tab.displayName = "Tab";

const ContentSection = styled.div`
  background-color: white;
  flex-grow: 1;
  padding: 40px 24px;
  min-height: 46vh;

  @media (max-width: 768px) {
    padding: 24px 16px;
  }
`;
ContentSection.displayName = "ContentSection";

const SearchResultsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
SearchResultsContainer.displayName = "SearchResultsContainer";

const SearchResultsGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
  transition: opacity 0.3s ease-in-out;

  /* Анимация появления карточек */
  & > * {
    animation: fadeInUp 0.4s ease-out;
    animation-fill-mode: both;
  }

  /* Задержки для плавного появления карточек по очереди */
  & > *:nth-child(1) {
    animation-delay: 0.05s;
  }
  & > *:nth-child(2) {
    animation-delay: 0.1s;
  }
  & > *:nth-child(3) {
    animation-delay: 0.15s;
  }
  & > *:nth-child(4) {
    animation-delay: 0.2s;
  }
  & > *:nth-child(5) {
    animation-delay: 0.25s;
  }
  & > *:nth-child(6) {
    animation-delay: 0.3s;
  }
  & > *:nth-child(7) {
    animation-delay: 0.35s;
  }
  & > *:nth-child(8) {
    animation-delay: 0.4s;
  }
  & > *:nth-child(9) {
    animation-delay: 0.45s;
  }
  & > *:nth-child(10) {
    animation-delay: 0.5s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
SearchResultsGrid.displayName = "SearchResultsGrid";

const SearchResultCard = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;
SearchResultCard.displayName = "SearchResultCard";

const SearchResultHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;
SearchResultHeader.displayName = "SearchResultHeader";

const SearchResultCode = styled.span`
  font-size: 12px;
  color: #6c757d;
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
`;
SearchResultCode.displayName = "SearchResultCode";

const SearchResultUnit = styled.span`
  font-size: 12px;
  color: #6c757d;
`;
SearchResultUnit.displayName = "SearchResultUnit";

const SearchResultTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 8px 0;
  line-height: 1.4;
`;
SearchResultTitle.displayName = "SearchResultTitle";

const SearchResultDescription = styled.p`
  font-size: 14px;
  color: #6c757d;
  margin: 8px 0 16px 0;
  line-height: 1.5;
`;
SearchResultDescription.displayName = "SearchResultDescription";

const AddButton = styled.button`
  background-color: ${(props) => (props.isSelected ? "#0066cc" : "white")};
  color: ${(props) => (props.isSelected ? "white" : "#0066cc")};
  border: ${(props) => (props.isSelected ? "none" : "1px solid #0066cc")};
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.isSelected ? "#0055b3" : "rgba(0, 91, 209, 0.1)"};
  }
`;
AddButton.displayName = "AddButton";

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
`;
NoResultsMessage.displayName = "NoResultsMessage";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
`;
LoadingMessage.displayName = "LoadingMessage";

const Footer = styled.div`
  background-color: white;
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid #b9bdc3;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;
Footer.displayName = "Footer";

const FooterText = styled.p`
  color: #7a7a7a;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  margin: 0;
`;
FooterText.displayName = "FooterText";

const CreateTender = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  // Инициализируем состояние из localStorage
  const [selectedProducts, setSelectedProducts] = useState(() => {
    try {
      const saved = localStorage.getItem("selectedTenderProducts");
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error("Ошибка при загрузке данных из localStorage:", error);
      return [];
    }
  });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Дебаунсинг поискового запроса с анимацией перехода
  useEffect(() => {
    if (searchQuery !== debouncedSearchQuery) {
      setIsTransitioning(true);
    }

    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setIsTransitioning(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, debouncedSearchQuery]);

  // Получаем результаты поиска без пагинации
  const {
    data: searchResults = [],
    isLoading: isSearchLoading,
    error: searchError,
  } = useSearchProducts(debouncedSearchQuery, {
    enabled: !!debouncedSearchQuery.trim(),
  });

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setDebouncedSearchQuery("");
  };

  const handleAddToTender = (product) => {
    // Проверяем, не добавлен ли уже этот товар
    const isAlreadyAdded = selectedProducts.some(
      (item) => item.MaterialId === product.MaterialId
    );

    if (!isAlreadyAdded) {
      setSelectedProducts((prev) => [...prev, product]);
      console.log("Товар добавлен в тендер:", product);
    } else {
      console.log("Товар уже добавлен в тендер");
    }
  };

  const handleRemoveFromTender = (productId) => {
    setSelectedProducts((prev) =>
      prev.filter((item) => item.MaterialId !== productId)
    );
  };

  const handleClearAllProducts = () => {
    setSelectedProducts([]);
    // localStorage очистится автоматически через useEffect
  };

  const handleCreateTender = () => {
    if (selectedProducts.length === 0) {
      alert("Выберите хотя бы один товар для создания тендера");
      return;
    }

    // Переходим на страницу с формами, передавая выбранные товары
    navigate("/tender-form", {
      state: { selectedProducts },
    });
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Сохранение выбранных товаров в localStorage
  useEffect(() => {
    try {
      localStorage.setItem(
        "selectedTenderProducts",
        JSON.stringify(selectedProducts)
      );
    } catch (error) {
      console.error("Ошибка при сохранении данных в localStorage:", error);
    }
  }, [selectedProducts]);

  // Отслеживание изменений localStorage (для синхронизации с TenderForm)
  useEffect(() => {
    const handleStorageChange = (event) => {
      // Обрабатываем как стандартные события storage, так и кастомные
      if (event.type === "storage" && event.key !== "selectedTenderProducts") {
        return;
      }

      try {
        let updatedProducts = [];

        if (event.type === "localStorageChange") {
          // Кастомное событие из TenderForm
          updatedProducts = event.detail.value;
        } else {
          // Стандартное событие storage
          const saved = localStorage.getItem("selectedTenderProducts");
          updatedProducts = saved ? JSON.parse(saved) : [];
        }

        setSelectedProducts(updatedProducts);
      } catch (error) {
        console.error("Ошибка при загрузке данных из localStorage:", error);
        setSelectedProducts([]);
      }
    };

    // Слушаем изменения localStorage (для других табов)
    window.addEventListener("storage", handleStorageChange);

    // Слушаем кастомные события (для того же таба)
    window.addEventListener("localStorageChange", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("localStorageChange", handleStorageChange);
    };
  }, []);

  // Закрытие выпадающего списка при клике вне его
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDropdownOpen && !event.target.closest("[data-dropdown]")) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  const tabs = [
    { id: "all", label: "ВСЕ РЕЗУЛЬТАТЫ" },
    { id: "materials", label: "СТРОИТЕЛЬНЫЕ МАТЕРИАЛЫ" },
    { id: "equipment", label: "ОБОРУДОВАНИЕ" },
    { id: "tools", label: "ИНСТРУМЕНТЫ" },
  ];

  return (
    <>
      <CreateTenderContainer>
        {/* Панель с выбранными товарами */}
        {selectedProducts.length > 0 && (
          <SelectedMainPanel>
            <SelectedProductsPanel>
              <SelectedProductsInfo>
                <DropdownContainer data-dropdown>
                  <PositionsButton
                    onClick={toggleDropdown}
                    isOpen={isDropdownOpen}
                  >
                    <img
                      src="/icons/ColumnTask2.svg"
                      width={"18"}
                      height={"18"}
                      alt="Иконка плюс"
                    />
                    позиций в списке: {selectedProducts.length}
                    {/* <span className="dropdown-arrow">▼</span> */}
                  </PositionsButton>
                  {isDropdownOpen && (
                    <DropdownList>
                      {selectedProducts.map((product) => (
                        <DropdownItem key={product.MaterialId}>
                          <span className="product-name">
                            {product.MaterialName}
                          </span>
                          <button
                            className="remove-btn"
                            onClick={() =>
                              handleRemoveFromTender(product.MaterialId)
                            }
                            title="Удалить из списка"
                          >
                            ×
                          </button>
                        </DropdownItem>
                      ))}
                      <CreateTenderButton
                        onClick={handleCreateTender}
                        style={{
                          justifyContent: "space-between",
                          width: "100%",
                          borderRadius: "0",
                          fontWeight: "500",
                          padding: "16px 16px",
                        }}
                      >
                        Создать тендер
                        <img
                          src="/icons/CheckCreateTender.svg"
                          width={"15"}
                          height={"15"}
                        />
                      </CreateTenderButton>
                    </DropdownList>
                  )}
                </DropdownContainer>
              </SelectedProductsInfo>
              {/* <Spacer /> */}
              <ActionButtons>
                <ClearAllButton onClick={handleClearAllProducts}>
                  <img
                    src="/icons/BusketCreateTender.svg"
                    width={"13"}
                    height={"13"}
                  />
                  ОЧИСТИТЬ ВСЕ
                </ClearAllButton>
                <CreateTenderButton onClick={handleCreateTender}>
                  СОЗДАТЬ ТЕНДЕР
                  <img
                    src="/icons/CheckCreateTender.svg"
                    width={"15"}
                    height={"15"}
                  />
                </CreateTenderButton>
              </ActionButtons>
            </SelectedProductsPanel>
          </SelectedMainPanel>
        )}
        <Title>
          Создайте тендер на закуп материала, инструмента,
          <br />
          оборудования
        </Title>
        <IconsContainer>
          <IconBlock>
            <IconWrapper>
              <Icon src="icons/search.svg" />
            </IconWrapper>
            <IconLabel>Найдите материал</IconLabel>
          </IconBlock>

          <IconBlock>
            <IconWrapper>
              <Icon src="icons/plus2.svg" />
            </IconWrapper>
            <IconLabel>Добавьте в список закупок</IconLabel>
          </IconBlock>

          <IconBlock>
            <IconWrapper>
              <Icon src="icons/calendar.svg" />
            </IconWrapper>
            <IconLabel>Укажите срок поставки</IconLabel>
          </IconBlock>

          <IconBlock>
            <IconWrapper>
              <Icon
                src="icons/ColumnTask.svg"
                style={{ transform: "translate(2px, 2px)" }}
              />
            </IconWrapper>
            <IconLabel>Опубликуйте тендер</IconLabel>
          </IconBlock>
        </IconsContainer>
        <SearchContainer>
          <SearchIcon>
            <img src="icons/search.svg"></img>
          </SearchIcon>
          <SearchInput
            type="text"
            placeholder="Поиск материалов..."
            value={searchQuery}
            onChange={handleSearchChange}
          />
          {searchQuery && <ClearButton onClick={clearSearch}>×</ClearButton>}
        </SearchContainer>

        <TabsContainer>
          {tabs.map((tab) => (
            <Tab
              key={tab.id}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </Tab>
          ))}
        </TabsContainer>

        <ContentSection>
          <SearchResultsContainer>
            {/* Счетчик результатов поиска */}
            {debouncedSearchQuery.trim() &&
              !isSearchLoading &&
              !searchError && (
                <ResultsCounter>
                  {searchResults.length === 0
                    ? "Ничего не найдено"
                    : `Найдено: ${searchResults.length} ${
                        searchResults.length === 1
                          ? "товар"
                          : searchResults.length < 5
                          ? "товара"
                          : "товаров"
                      }`}
                </ResultsCounter>
              )}
            {/* Показываем результаты поиска только если есть поисковый запрос */}
            {debouncedSearchQuery.trim() ? (
              <>
                {isSearchLoading ? (
                  <LoadingMessage>Поиск товаров...</LoadingMessage>
                ) : searchError ? (
                  <NoResultsMessage>
                    Ошибка поиска: {searchError.message}
                  </NoResultsMessage>
                ) : searchResults.length === 0 ? (
                  <NoResultsMessage>
                    По запросу "{debouncedSearchQuery}" ничего не найдено
                  </NoResultsMessage>
                ) : (
                  <SearchResultsGrid
                    style={{
                      opacity: isTransitioning ? 0.3 : 1,
                      transform: isTransitioning
                        ? "translateY(10px)"
                        : "translateY(0)",
                      transition:
                        "opacity 0.3s ease-in-out, transform 0.3s ease-in-out",
                    }}
                  >
                    {searchResults.map((product, index) => (
                      <SearchResultCard
                        key={product.MaterialId}
                        style={{
                          animationDelay: `${index * 0.05}s`,
                        }}
                      >
                        <SearchResultHeader>
                          <SearchResultCode>
                            {product.MaterialId}
                          </SearchResultCode>
                          <SearchResultUnit>
                            Единица измерения: {product.UnitName || "Штуки"}
                          </SearchResultUnit>
                        </SearchResultHeader>
                        <SearchResultTitle>
                          {product.MaterialName}
                        </SearchResultTitle>
                        <SearchResultDescription>
                          Продается в штуках, используется для выкладки стен и
                          иных несущих конструкций.
                        </SearchResultDescription>

                        <AddButton
                          isSelected={selectedProducts.some(
                            (item) => item.MaterialId === product.MaterialId
                          )}
                          onClick={() =>
                            selectedProducts.some(
                              (item) => item.MaterialId === product.MaterialId
                            )
                              ? handleRemoveFromTender(product.MaterialId)
                              : handleAddToTender(product)
                          }
                        >
                          {selectedProducts.some(
                            (item) => item.MaterialId === product.MaterialId
                          )
                            ? "Убрать"
                            : "Добавить"}
                        </AddButton>
                      </SearchResultCard>
                    ))}
                  </SearchResultsGrid>
                )}
              </>
            ) : (
              <NoResultsMessage>
                Введите название материала в поисковую строку выше
              </NoResultsMessage>
            )}
          </SearchResultsContainer>
        </ContentSection>

        <Footer>
          <FooterText>ВСЕ ПРАВА ПРИНАДЛЕЖАТ ТОО NURSADI</FooterText>
        </Footer>
      </CreateTenderContainer>
    </>
  );
};

export default CreateTender;
