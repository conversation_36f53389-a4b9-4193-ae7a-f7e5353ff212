* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* border: solid 1px red; */
}

html,
body {
  overflow-x: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  /* background-color: white; */
  position: relative;
  width: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

ul,
ol {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}
